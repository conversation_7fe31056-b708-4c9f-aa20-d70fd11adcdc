"""
Support system models for tickets, FAQ, and live chat
"""

import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinLengthValidator
from django.urls import reverse

User = get_user_model()


class SupportCategory(models.Model):
    """Categories for support tickets and FAQ articles

    Reverse relationships:
    - tickets: Related SupportTicket objects
    - faq_articles: Related FAQArticle objects (via FAQCategory)
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="CSS icon class")
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['sort_order', 'name']
        verbose_name_plural = 'Support Categories'

    def __str__(self):
        return self.name

    def get_ticket_count(self):
        """Get number of open tickets in this category"""
        from .models import SupportTicket
        return SupportTicket.objects.filter(
            category=self,
            status__in=['open', 'in_progress']
        ).count()

    def get_total_ticket_count(self):
        """Get total number of tickets in this category"""
        from .models import SupportTicket
        return SupportTicket.objects.filter(category=self).count()


class SupportTicket(models.Model):
    """Support ticket model with comprehensive tracking

    Reverse relationships:
    - responses: Related TicketResponse objects
    """

    STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('waiting_customer', 'Waiting for Customer'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
        ('escalated', 'Escalated'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
        ('critical', 'Critical'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ticket_number = models.CharField(max_length=20, unique=True, editable=False)

    # User information
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_tickets')

    # Ticket details
    category = models.ForeignKey(SupportCategory, on_delete=models.SET_NULL, null=True, related_name='tickets')
    subject = models.CharField(max_length=200, validators=[MinLengthValidator(5)])
    description = models.TextField(validators=[MinLengthValidator(10)])

    # Status and priority
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='normal')

    # Assignment
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_tickets',
        limit_choices_to={'is_staff': True}
    )

    # Resolution
    resolution = models.TextField(blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_tickets'
    )

    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_response_at = models.DateTimeField(auto_now_add=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['assigned_to', 'status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"#{self.ticket_number} - {self.subject}"

    def save(self, *args, **kwargs):
        if not self.ticket_number:
            self.ticket_number = self.generate_ticket_number()
        super().save(*args, **kwargs)

    def generate_ticket_number(self):
        """Generate unique ticket number"""
        import random
        import string

        # Format: BZ-YYYYMMDD-XXXX (BZ = Betzide)
        date_str = timezone.now().strftime('%Y%m%d')
        random_str = ''.join(random.choices(string.digits, k=4))
        return f"BZ-{date_str}-{random_str}"

    def get_absolute_url(self):
        return reverse('support:ticket_detail', kwargs={'ticket_id': str(self.id)})

    @property
    def is_open(self):
        return self.status in ['open', 'in_progress', 'waiting_customer']

    @property
    def response_time(self):
        """Calculate response time in hours"""
        from .models import TicketResponse
        responses = TicketResponse.objects.filter(ticket=self)
        if responses.exists():
            first_response = responses.filter(is_staff_response=True).first()
            if first_response:
                delta = first_response.created_at - self.created_at
                return delta.total_seconds() / 3600
        return None

    @property
    def age_in_hours(self):
        """Calculate ticket age in hours"""
        delta = timezone.now() - self.created_at
        return delta.total_seconds() / 3600

    def close_ticket(self, resolved_by=None, resolution=''):
        """Close the ticket"""
        self.status = 'closed'
        self.resolved_at = timezone.now()
        self.resolved_by = resolved_by
        if resolution:
            self.resolution = resolution
        self.save(update_fields=['status', 'resolved_at', 'resolved_by', 'resolution'])

    def escalate_ticket(self):
        """Escalate the ticket"""
        self.status = 'escalated'
        self.priority = 'high' if self.priority in ['low', 'normal'] else self.priority
        self.save(update_fields=['status', 'priority'])

    def get_responses_count(self):
        """Get number of responses"""
        from .models import TicketResponse
        return TicketResponse.objects.filter(ticket=self).count()

    def get_last_response(self):
        """Get the last response"""
        from .models import TicketResponse
        return TicketResponse.objects.filter(ticket=self).order_by('-created_at').first()


class TicketResponse(models.Model):
    """Responses to support tickets"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='responses')
    user = models.ForeignKey(User, on_delete=models.CASCADE)

    # Response content
    message = models.TextField(validators=[MinLengthValidator(5)])
    is_staff_response = models.BooleanField(default=False)
    is_internal_note = models.BooleanField(default=False)  # Internal notes not visible to customer

    # Attachments
    attachment = models.FileField(upload_to='support/attachments/', blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['ticket', 'created_at']),
            models.Index(fields=['is_staff_response']),
        ]

    def __str__(self):
        response_type = "Staff" if self.is_staff_response else "Customer"
        return f"{response_type} response to {self.ticket.ticket_number}"

    def save(self, *args, **kwargs):
        # Automatically set is_staff_response based on user
        if self.user.is_staff:
            self.is_staff_response = True

        super().save(*args, **kwargs)

        # Update ticket's last response time
        self.ticket.last_response_at = self.created_at
        self.ticket.save(update_fields=['last_response_at'])


class FAQCategory(models.Model):
    """Categories for FAQ articles

    Reverse relationships:
    - articles: Related FAQArticle objects
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True)
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['sort_order', 'name']
        verbose_name_plural = 'FAQ Categories'

    def __str__(self):
        return self.name

    def get_published_articles_count(self):
        from .models import FAQArticle
        return FAQArticle.objects.filter(category=self, is_published=True).count()


class FAQArticle(models.Model):
    """FAQ articles with search functionality"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    category = models.ForeignKey(FAQCategory, on_delete=models.CASCADE, related_name='articles')

    # Content
    question = models.CharField(max_length=300, validators=[MinLengthValidator(10)])
    answer = models.TextField(validators=[MinLengthValidator(20)])
    keywords = models.CharField(max_length=500, blank=True, help_text="Comma-separated keywords for search")

    # Publishing
    is_published = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)

    # Statistics
    view_count = models.PositiveIntegerField(default=0)
    helpful_votes = models.PositiveIntegerField(default=0)
    unhelpful_votes = models.PositiveIntegerField(default=0)

    # Authoring
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_faqs')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_faqs')

    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category__sort_order', 'sort_order', 'question']
        indexes = [
            models.Index(fields=['is_published', 'category']),
            models.Index(fields=['view_count']),
        ]

    def __str__(self):
        return self.question

    def get_absolute_url(self):
        return reverse('support:faq_detail', kwargs={'article_id': str(self.id)})

    @property
    def helpfulness_ratio(self):
        """Calculate helpfulness ratio"""
        total_votes = self.helpful_votes + self.unhelpful_votes
        if total_votes == 0:
            return 0
        return (self.helpful_votes / total_votes) * 100

    def increment_view_count(self):
        """Increment view count"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def vote_helpful(self):
        """Add helpful vote"""
        self.helpful_votes += 1
        self.save(update_fields=['helpful_votes'])

    def vote_unhelpful(self):
        """Add unhelpful vote"""
        self.unhelpful_votes += 1
        self.save(update_fields=['unhelpful_votes'])


class ChatSession(models.Model):
    """Live chat session between user and support agent

    Reverse relationships:
    - messages: Related ChatMessage objects
    """

    STATUS_CHOICES = [
        ('waiting', 'Waiting for Agent'),
        ('active', 'Active'),
        ('ended', 'Ended'),
        ('abandoned', 'Abandoned'),
    ]

    RATING_CHOICES = [
        (1, '1 - Very Poor'),
        (2, '2 - Poor'),
        (3, '3 - Average'),
        (4, '4 - Good'),
        (5, '5 - Excellent'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session_id = models.CharField(max_length=50, unique=True, editable=False)

    # Participants
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_sessions')
    agent = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='agent_chat_sessions',
        limit_choices_to={'is_staff': True}
    )

    # Session details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='waiting')
    subject = models.CharField(max_length=200, blank=True)

    # Timing
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    last_activity_at = models.DateTimeField(auto_now=True)

    # Ratings
    user_rating = models.PositiveSmallIntegerField(null=True, blank=True, choices=RATING_CHOICES)
    user_feedback = models.TextField(blank=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['status', 'started_at']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['agent', 'status']),
        ]

    def __str__(self):
        # CustomUser has phone_number attribute
        user_identifier = getattr(self.user, 'phone_number', getattr(self.user, 'username', str(self.user.pk)))
        return f"Chat {self.session_id} - {user_identifier}"

    def save(self, *args, **kwargs):
        if not self.session_id:
            self.session_id = self.generate_session_id()
        super().save(*args, **kwargs)

    def generate_session_id(self):
        """Generate unique session ID"""
        import random
        import string

        timestamp = timezone.now().strftime('%Y%m%d%H%M')
        random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        return f"CHAT-{timestamp}-{random_str}"

    @property
    def duration(self):
        """Calculate session duration"""
        end_time = self.ended_at or timezone.now()
        return end_time - self.started_at

    @property
    def is_active(self):
        return self.status == 'active'

    @property
    def message_count(self):
        from .models import ChatMessage
        return ChatMessage.objects.filter(session=self).count()

    def end_session(self, ended_by=None):
        """End the chat session"""
        self.status = 'ended'
        self.ended_at = timezone.now()
        if ended_by:
            self.metadata['ended_by'] = str(ended_by.id)
        self.save(update_fields=['status', 'ended_at', 'metadata'])

    def assign_agent(self, agent):
        """Assign an agent to the session"""
        self.agent = agent
        self.status = 'active'
        self.save(update_fields=['agent', 'status'])

    def get_messages_count(self):
        from .models import ChatMessage
        return ChatMessage.objects.filter(session=self).count()

    def get_last_message(self):
        from .models import ChatMessage
        return ChatMessage.objects.filter(session=self).order_by('-created_at').first()


class ChatMessage(models.Model):
    """Individual messages in a chat session"""

    MESSAGE_TYPES = [
        ('text', 'Text Message'),
        ('system', 'System Message'),
        ('file', 'File Attachment'),
        ('image', 'Image'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(ChatSession, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE)

    # Message content
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='text')
    content = models.TextField()

    # File attachments
    attachment = models.FileField(upload_to='chat/attachments/', blank=True, null=True)

    # Message status
    is_read = models.BooleanField(default=False)
    is_system_message = models.BooleanField(default=False)

    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['session', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
            models.Index(fields=['is_read']),
        ]

    def __str__(self):
        # CustomUser has phone_number attribute
        sender_identifier = getattr(self.sender, 'phone_number', getattr(self.sender, 'username', str(self.sender.pk)))
        return f"Message from {sender_identifier} in {self.session.session_id}"

    def mark_as_read(self, read_by=None):
        """Mark message as read"""
        self.is_read = True
        self.read_at = timezone.now()
        if read_by:
            self.metadata['read_by'] = str(read_by.id)
        self.save(update_fields=['is_read', 'read_at', 'metadata'])

    @property
    def is_from_agent(self):
        return self.sender.is_staff

    @property
    def is_from_user(self):
        return not self.sender.is_staff


class SupportNotification(models.Model):
    """Notifications for support system events"""

    NOTIFICATION_TYPES = [
        ('ticket_created', 'Ticket Created'),
        ('ticket_updated', 'Ticket Updated'),
        ('ticket_response', 'Ticket Response'),
        ('chat_message', 'Chat Message'),
        ('chat_started', 'Chat Started'),
        ('chat_ended', 'Chat Ended'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_notifications')

    # Notification details
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=200)
    message = models.TextField()

    # Related objects
    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, null=True, blank=True)
    chat_session = models.ForeignKey(ChatSession, on_delete=models.CASCADE, null=True, blank=True)

    # Status
    is_read = models.BooleanField(default=False)
    is_sent = models.BooleanField(default=False)

    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['notification_type', 'created_at']),
        ]

    def __str__(self):
        # CustomUser has phone_number attribute
        recipient_identifier = getattr(self.recipient, 'phone_number', getattr(self.recipient, 'username', str(self.recipient.pk)))
        return f"{self.title} for {recipient_identifier}"

    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        self.read_at = timezone.now()
        self.save(update_fields=['is_read', 'read_at'])

    def mark_as_sent(self):
        """Mark notification as sent"""
        self.is_sent = True
        self.sent_at = timezone.now()
        self.save(update_fields=['is_sent', 'sent_at'])
